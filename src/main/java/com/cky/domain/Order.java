package com.cky.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础订单表
 *
 * @TableName order
 */
@TableName(value = "order")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Order {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号(唯一)
     */
    private String orderNo;

    /**
     * 订单名称/描述
     */
    private String orderName;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 下单时间
     */
    private Date createTime;

    /**
     * 订单状态(0:待支付 1:已支付 2:已取消 3:已完成)
     */
    private Integer status;

    // 状态枚举（可选）
    public enum Status {
        UNPAID(0, "待支付"),
        PAID(1, "已支付"),
        CANCELLED(2, "已取消"),
        COMPLETED(3, "已完成");

        private final int code;
        private final String desc;

        Status(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}