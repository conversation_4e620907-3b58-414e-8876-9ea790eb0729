package com.cky.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 订单编号生成工具类
 */
public class OrderNoGenerator {

    // 默认格式：年月日时分秒+4位随机数
    private static final String DEFAULT_PATTERN = "yyyyMMddHHmmss";
    private static final int DEFAULT_RANDOM_LENGTH = 4;
    
    // 序列号计数器（用于保证同一秒内的唯一性）
    private static final AtomicInteger sequence = new AtomicInteger(0);
    private static final int MAX_SEQUENCE = 9999;
    
    // 前缀（可选）
    private static String prefix = "";
    
    // 后缀（可选）
    private static String suffix = "";
    
    // 随机数生成器
    private static final java.util.Random random = new java.util.Random();

    /**
     * 生成默认格式的订单编号
     * @return 订单编号
     */
    public static String generate() {
        return generate(DEFAULT_PATTERN, DEFAULT_RANDOM_LENGTH);
    }

    /**
     * 生成带前缀的订单编号
     * @param prefix 前缀
     * @return 订单编号
     */
    public static String generateWithPrefix(String prefix) {
        return prefix + generate();
    }

    /**
     * 生成带后缀的订单编号
     * @param suffix 后缀
     * @return 订单编号
     */
    public static String generateWithSuffix(String suffix) {
        return generate() + suffix;
    }

    /**
     * 自定义格式生成订单编号
     * @param pattern 日期格式
     * @param randomLength 随机数长度
     * @return 订单编号
     */
    public static String generate(String pattern, int randomLength) {
        if (pattern == null || pattern.isEmpty()) {
            pattern = DEFAULT_PATTERN;
        }
        
        if (randomLength <= 0) {
            randomLength = DEFAULT_RANDOM_LENGTH;
        }
        
        // 1. 获取当前时间并格式化
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        String dateStr = sdf.format(new Date());
        
        // 2. 获取序列号（保证同一秒内的唯一性）
        int seq = sequence.incrementAndGet();
        if (seq > MAX_SEQUENCE) {
            sequence.set(0);
            seq = sequence.incrementAndGet();
        }
        
        // 3. 生成随机数
        String randomStr = generateRandomNum(randomLength);
        
        // 4. 拼接各部分
        return prefix + dateStr + String.format("%04d", seq) + randomStr + suffix;
    }

    /**
     * 生成指定位数的随机数字字符串
     * @param length 随机数长度
     * @return 随机数字字符串
     */
    private static String generateRandomNum(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    /**
     * 设置全局前缀
     * @param prefix 前缀
     */
    public static void setPrefix(String prefix) {
        OrderNoGenerator.prefix = prefix != null ? prefix : "";
    }

    /**
     * 设置全局后缀
     * @param suffix 后缀
     */
    public static void setSuffix(String suffix) {
        OrderNoGenerator.suffix = suffix != null ? suffix : "";
    }

    // 测试
    public static void main(String[] args) {
        // 测试默认生成
        System.out.println("默认订单号: " + OrderNoGenerator.generate());
        
        // 测试带前缀
        System.out.println("带前缀订单号: " + OrderNoGenerator.generateWithPrefix("ORD"));
        
        // 测试带后缀
        System.out.println("带后缀订单号: " + OrderNoGenerator.generateWithSuffix("END"));
        
        // 测试自定义格式
        System.out.println("自定义格式订单号: " + OrderNoGenerator.generate("yyMMdd", 6));
        
        // 测试设置全局前缀后缀
        OrderNoGenerator.setPrefix("P_");
        OrderNoGenerator.setSuffix("_S");
        System.out.println("全局前缀后缀订单号: " + OrderNoGenerator.generate());
    }
}