package com.cky.controller;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.cky.domain.AliPay;
import com.cky.domain.AliPayVo;
import com.cky.domain.Order;
import com.cky.service.OrderService;
import com.cky.util.OrderNoGenerator;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;


import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/aliPay")
public class AliPayController {

    private static final String FORMAT = "JSON";
    private static final String CHARSET = "UTF-8";
    //签名方式
    private static final String SIGN_TYPE = "RSA2";
    @Resource
    private AliPayVo aliPayConfig;

    @Autowired
    private OrderService orderService;

    /**
     * 下单
     * @param aliPay
     * @param httpResponse
     * @throws Exception
     */
    @PostMapping("/pay")
    public void pay(AliPay aliPay, HttpServletResponse httpResponse) throws Exception {
        System.out.println("  pay = " + aliPay);

        Order order = new Order();
        String orderNo = OrderNoGenerator.generate();
        order.setAmount(new BigDecimal(aliPay.getTotalAmount()));
        order.setStatus(Order.Status.UNPAID.getCode());
        order.setOrderNo(orderNo);
        order.setOrderName(aliPay.getSubject());
        orderService.save(order);

        // 1. 创建Client，通用SDK提供的Client，负责调用支付宝的API
        AlipayClient alipayClient = new DefaultAlipayClient(aliPayConfig.getGatewayUrl(), aliPayConfig.getAppId(),
                aliPayConfig.getAppPrivateKey(), FORMAT, CHARSET, aliPayConfig.getAlipayPublicKey(), SIGN_TYPE);

        // 2. 创建 Request并设置Request参数
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();  // 发送请求的 Request类
        request.setReturnUrl(aliPayConfig.getReturnUrl());
        request.setNotifyUrl(aliPayConfig.getNotifyUrl());
        
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", orderNo);      // 我们自己生成的订单编号
        bizContent.put("total_amount", aliPay.getTotalAmount()+""); // 订单的总金额
        bizContent.put("subject", aliPay.getSubject()+"");          // 订单名称
        bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");   // 固定配置

        request.setBizContent(bizContent.toString());

        // 执行请求，拿到响应的结果，返回给浏览器
        String form = "";
        try {
            form = alipayClient.pageExecute(request).getBody(); // 调用SDK生成表单
            System.out.println( form );
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        httpResponse.setContentType("text/html;charset=" + CHARSET);
        httpResponse.getWriter().write(form);// 直接将完整的表单html输出到页面
        httpResponse.getWriter().flush();
        httpResponse.getWriter().close();
    }


    @RequestMapping("/success")
    public String handlePayment(
            @RequestParam("total_amount") String amount,
            @RequestParam("out_trade_no") String out_trade_no,
            Model model) {
        System.out.println("=========支付宝同步回调========");

        System.out.println("处理支付 - 金额: " + amount + ", 订单号: " + out_trade_no);

        model.addAttribute("amount", amount);
        model.addAttribute("out_trade_no", out_trade_no);
        // 4. 跳转到支付成功页面
        return "successPage";
    }

    /**
     * 回调
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/notify")  // 注意这里必须是POST接口
    @ResponseBody
    public String payNotify(HttpServletRequest request) throws Exception {
        System.out.println("  payNotify  ");
        if (request.getParameter("trade_status").equals("TRADE_SUCCESS")) {
            System.out.println("=========支付宝异步回调========");
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();
            for (String name : requestParams.keySet()) {
                params.put(name, request.getParameter(name));
                // System.out.println(name + " = " + request.getParameter(name));
            }

            String tradeNo = params.get("out_trade_no");
            String gmtPayment = params.get("gmt_payment");
            String alipayTradeNo = params.get("trade_no");

            String sign = params.get("sign");
            String content = AlipaySignature.getSignCheckContentV1(params);
            boolean checkSignature = AlipaySignature.rsa256CheckContent(content, sign, aliPayConfig.getAlipayPublicKey(), "UTF-8"); // 验证签名
            // 支付宝验签
            if (checkSignature) {
                // 验签通过
                System.out.println("交易名称: " + params.get("subject"));
                System.out.println("交易状态: " + params.get("trade_status"));
                System.out.println("支付宝交易凭证号: " + params.get("trade_no"));
                System.out.println("商户订单号: " + params.get("out_trade_no"));
                System.out.println("交易金额: " + params.get("total_amount"));
                System.out.println("买家在支付宝唯一id: " + params.get("buyer_id"));
                System.out.println("买家付款时间: " + params.get("gmt_payment"));
                System.out.println("买家付款金额: " + params.get("buyer_pay_amount"));

                QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("order_no",params.get("out_trade_no"));
                Order order = orderService.getOne(queryWrapper);
                order.setStatus(Order.Status.PAID.getCode());
                orderService.updateById(order);
            }
        }
        return "success";
    }



}
