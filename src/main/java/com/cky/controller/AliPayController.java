package com.cky.controller;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.cky.domain.AliPay;
import com.cky.domain.AliPayVo;
import com.cky.domain.Order;
import com.cky.service.OrderService;
import com.cky.util.OrderNoGenerator;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;


import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/aliPay")
public class AliPayController {

    private static final String FORMAT = "JSON";
    private static final String CHARSET = "UTF-8";
    //签名方式
    private static final String SIGN_TYPE = "RSA2";
    @Resource
    private AliPayVo aliPayConfig;

    @Autowired
    private OrderService orderService;

    /**
     * 下单
     * @param aliPay
     * @param httpResponse
     * @throws Exception
     */
    @PostMapping("/pay")
    public void pay(AliPay aliPay, HttpServletResponse httpResponse) throws Exception {
        System.out.println("  pay = " + aliPay);

        Order order = new Order();
        String orderNo = OrderNoGenerator.generate();
        order.setAmount(new BigDecimal(aliPay.getTotalAmount()));
        order.setStatus(Order.Status.UNPAID.getCode());
        order.setOrderNo(orderNo);
        order.setOrderName(aliPay.getSubject());
        orderService.save(order);

        // 1. 创建Client，通用SDK提供的Client，负责调用支付宝的API
        AlipayClient alipayClient = new DefaultAlipayClient(aliPayConfig.getGatewayUrl(), aliPayConfig.getAppId(),
                aliPayConfig.getAppPrivateKey(), FORMAT, CHARSET, aliPayConfig.getAlipayPublicKey(), SIGN_TYPE);

        // 2. 创建 Request并设置Request参数
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();  // 发送请求的 Request类
        request.setReturnUrl(aliPayConfig.getReturnUrl());
        request.setNotifyUrl(aliPayConfig.getNotifyUrl());
        
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", orderNo);      // 我们自己生成的订单编号
        bizContent.put("total_amount", aliPay.getTotalAmount()+""); // 订单的总金额
        bizContent.put("subject", aliPay.getSubject()+"");          // 订单名称
        bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");   // 固定配置

        request.setBizContent(bizContent.toString());

        // 执行请求，拿到响应的结果，返回给浏览器
        String form = "";
        try {
            form = alipayClient.pageExecute(request).getBody(); // 调用SDK生成表单
            System.out.println( form );
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        httpResponse.setContentType("text/html;charset=" + CHARSET);
        httpResponse.getWriter().write(form);// 直接将完整的表单html输出到页面
        httpResponse.getWriter().flush();
        httpResponse.getWriter().close();
    }


    @RequestMapping("/success")
    public String handlePayment(HttpServletRequest request, Model model) {
        System.out.println("===================支付宝同步回调开始===================");

        // 获取所有参数
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> parameterMap = request.getParameterMap();

        // 打印所有接收到的参数
        System.out.println("接收到的所有同步回调参数：");
        for (String key : parameterMap.keySet()) {
            String value = request.getParameter(key);
            params.put(key, value);
            System.out.println(key + " = " + value);
        }

        // 验证同步回调签名
        try {
            String sign = params.get("sign");
            String content = AlipaySignature.getSignCheckContentV1(params);
            boolean checkSignature = AlipaySignature.rsa256CheckContent(content, sign, aliPayConfig.getAlipayPublicKey(), "UTF-8");

            System.out.println("===================同步回调验签结果===================");
            System.out.println("验签结果: " + (checkSignature ? "成功" : "失败"));

            if (!checkSignature) {
                System.out.println("警告：同步回调验签失败！可能存在安全风险！");
            }
        } catch (Exception e) {
            System.out.println("同步回调验签过程中发生异常: " + e.getMessage());
        }

        String amount = request.getParameter("total_amount");
        String outTradeNo = request.getParameter("out_trade_no");
        String tradeNo = request.getParameter("trade_no");
        String tradeStatus = request.getParameter("trade_status");

        System.out.println("===================关键信息===================");
        System.out.println("支付金额: " + amount);
        System.out.println("商户订单号: " + outTradeNo);
        System.out.println("支付宝交易号: " + tradeNo);
        System.out.println("交易状态: " + tradeStatus);

        model.addAttribute("amount", amount);
        model.addAttribute("out_trade_no", outTradeNo);
        model.addAttribute("trade_no", tradeNo);
        model.addAttribute("trade_status", tradeStatus);

        System.out.println("===================跳转到成功页面===================");
        return "successPage";
    }

    /**
     * 回调
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/notify")  // 注意这里必须是POST接口
    @ResponseBody
    public String payNotify(HttpServletRequest request) throws Exception {
        System.out.println("===================支付宝异步回调开始===================");

        // 获取支付宝POST过来反馈信息
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();

        // 打印所有接收到的参数
        System.out.println("接收到的所有参数：");
        for (String name : requestParams.keySet()) {
            String value = request.getParameter(name);
            params.put(name, value);
            System.out.println(name + " = " + value);
        }

        // 检查交易状态
        String tradeStatus = params.get("trade_status");
        System.out.println("当前交易状态: " + tradeStatus);

        if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
            System.out.println("交易状态符合条件，开始验签...");

            try {
                // 调用SDK验证签名
                String sign = params.get("sign");
                String content = AlipaySignature.getSignCheckContentV1(params);
                boolean checkSignature = AlipaySignature.rsa256CheckContent(content, sign, aliPayConfig.getAlipayPublicKey(), "UTF-8");

                System.out.println("验签结果: " + (checkSignature ? "成功" : "失败"));

                if (checkSignature) {
                    System.out.println("===================验签成功，开始处理业务===================");

                    // 输出关键交易信息
                    System.out.println("交易名称: " + params.get("subject"));
                    System.out.println("交易状态: " + params.get("trade_status"));
                    System.out.println("支付宝交易凭证号: " + params.get("trade_no"));
                    System.out.println("商户订单号: " + params.get("out_trade_no"));
                    System.out.println("交易金额: " + params.get("total_amount"));
                    System.out.println("买家在支付宝唯一id: " + params.get("buyer_id"));
                    System.out.println("买家付款时间: " + params.get("gmt_payment"));
                    System.out.println("买家付款金额: " + params.get("buyer_pay_amount"));

                    // 更新订单状态
                    String outTradeNo = params.get("out_trade_no");
                    if (outTradeNo != null && !outTradeNo.isEmpty()) {
                        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("order_no", outTradeNo);
                        Order order = orderService.getOne(queryWrapper);

                        if (order != null) {
                            System.out.println("找到订单，更新状态为已支付");
                            order.setStatus(Order.Status.PAID.getCode());
                            orderService.updateById(order);
                            System.out.println("订单状态更新成功");
                        } else {
                            System.out.println("警告：未找到对应的订单记录，订单号: " + outTradeNo);
                        }
                    }

                    System.out.println("===================业务处理完成===================");
                } else {
                    System.out.println("验签失败！可能的原因：");
                    System.out.println("1. 公钥配置错误");
                    System.out.println("2. 签名被篡改");
                    System.out.println("3. 参数编码问题");
                }
            } catch (Exception e) {
                System.out.println("验签过程中发生异常: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            System.out.println("交易状态不符合处理条件，当前状态: " + tradeStatus);
        }

        System.out.println("===================支付宝异步回调结束===================");
        return "success";
    }



}
