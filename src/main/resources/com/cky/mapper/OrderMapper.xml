<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cky.mapper.OrderMapper">

    <resultMap id="BaseResultMap" type="com.cky.domain.Order">
            <id property="id" column="id" />
            <result property="orderNo" column="order_no" />
            <result property="orderName" column="order_name" />
            <result property="amount" column="amount" />
            <result property="createTime" column="create_time" />
            <result property="status" column="status" />
    </resultMap>

    <sql id="Base_Column_List">
        id,order_no,order_name,amount,create_time,status
    </sql>
</mapper>
