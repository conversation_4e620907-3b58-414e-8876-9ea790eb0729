alipay:
  appId: 9021000151612494
  appPrivateKey: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCA9Yc6OwM84Aw8EXlMlutKtRXu2AmCdbD/hCO1/Su8KXVTyarUyuLrpjpYnaVme2tavhsR0YZKKp58ipsRhVcLr+8RfCOE8QAltKit+dSLDj8CvG8lMuY20JQ2Cl3RabPtzSxp3x6mOrEyTdHlhstRIIGboJRdb8bUrugzMhsgCsJbY4TxvGFgetVZibgS2XVjQ6c+Gzbb00ADeN/zPRWNYli/xMMw4gqtV0vUQIp56dFNmvlOWINss2hfPmu9sndowX0xuN/FnMJE5n8Fk6mdkgQ3lbIKBhwh789Rn9idnreI50KMK81hvQilRKL5zz5JO9Cyf2Z2ACswSK92IbkPAgMBAAECggEAf89YQ+0i611+orYKy060SimpYDu7SRJN5yBWqFMP2oAYaX9xEU83kagBIby0phpiLuBFQeawOqd8xiA1SismN04aGfE33tVgeP5RQ4RdE6h96L9LRMnqgp88lUa27wrdKi1z/EYHQWbgIeQgyGRM883MxqpjASv8GuRy+/KIEbtLIP1WiyEayg0SSXXZOrySY/mxQ6/RyCPyyu2ABSTp6YpQlPWzHfO1oZxbV2JZGYils8tn3rQfDvueO3SjVl5EASYvAvMkKYnF9M6NHs0lLtxLPAvhG02ddgyNG0MIibtLIdcK74AWMWbesOOULEX8y7xLqvMsgXd+EikQN9qMeQKBgQDWAT43jKxV5rX1DAugdT4tpzpzwZIXXe97WfPYtFPLb9v0X9bb6GEfHb6oMG3/avjXbBVd/ekdHV8J6Be90AmEHnHnvfstAo7NZySRZ2lwtjgNSmsJj5Ziut7bHdE0WLwLL8W3dUfVTvLXnUTrpWm9LTS9RnpkeH5v91xo0RpgrQKBgQCaQ+w54Sb90ICJVOXSJtP6uyALRJCS/fa026bncdhZexAvDThDjPoHyY6l707CdTkpCHYtg0UtZJslbzKygRMPkUsL9X0GXcVdDXIFt7IZW4EBvnYuUQzAPfpenVTRCA9SKZ0n0keyftk3Jy51uCB2z9qChswmC/qxJ1cZimTsKwKBgBCrfh4zngT++LOC4bD5EQPbX+k+bdPWJF52blE2EsTpDLgncwQkQNR8q6pMQ3U+7u1Q5QUyTyNOc2XBJ4BIoUUiXfsQSG2EIn2LiESvSn5WI12dq2LORYehSNTGqNIsC1HPwdX9k7/g63eyL3kvpGHxEIeh4szKugacvR0JFMXtAoGABfvId95B0up5cdo3J9WVt032yjkYmnr7TLyQVr3VtHLPh+OtYQFg2Vh6DfmgRm6hjvPjEZTb5AzY77TGdKIJCTuLe6VD+jE87IUO/SXI5K7l7BllIBrIsdQ1l+HmXk0/uuIeVGMVoNjGuBru9GX17GXXheSQmpEwYhqyiC0UCQ0CgYAYBfHxKmxR9zdBLuGAVR9eakz4nPXj/1lyH5T1jSq5i5MNcEaL4XtC7m6iwfIpHI2PQtYf/avT3p7KzyOPbOBZUQ3O200xIfIFIRg2vhon8Vjgpg2Xp0MaSoqX45Aj39xTKO5d9smj7s+e2GNet8MSUHCX2ZxBf8YhSIjt9vbhKg==
  alipayPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArGmMUv6zL4Vclm1LU9LKE+m1kARdoQUQQVyX+thjZKNualcTDBecT4CYcSAgggUlu6yvO5YBNlVfdCRchOibAU4Pa+mRNWMYDuGLQrz2d9FKMGStKE/NEZ/JVlFlFOiP86dAYJ5R6jYtwtAEz90ffiJqTbIk4A9bmFTc/S9RAxBGMIA6YoU8hBt3fFPuyokKZuDqQ4QjGxdwFiMJLBHiCmMVJQmy6lC7VqL3qStAkob45qpsh4Vlm2fU0exJDWBMhIhcg8J+US4N1eIoABRfhs0tQc+aYThBjJrl5aN4DkLFDofjS3YI2CJXVgRRgcLmqAg3zFJ8wmo7OLqI57jqqQIDAQAB
  notifyUrl: http://localhost:8080/aliPay/notify
  returnUrl: http://localhost:8080/aliPay/success
  gatewayUrl: https://openapi-sandbox.dl.alipaydev.com/gateway.do
spring:
  datasource:
    url: ****************************************************************************
    username: root
    password: 020820
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: is_delete # 全局逻辑删除字段名
      logic-delete-value: 1 # 逻辑已删除值。可选，默认值为 1
      logic-not-delete-value: 0 # 逻辑未删除值。可选，默认值为 0
  mapper-locations: classpath*:/mapper/**/*.xml # 默认值
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl