<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付成功</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8fafa;
        }
        .success-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            text-align: center;
        }
        .success-icon {
            font-size: 60px;
            color: #52c41a;
            margin-bottom: 20px;
        }
        .order-info {
            text-align: left;
            background-color: #f8f8f8;
            padding: 15px;
            border-radius: 8px;
            margin: 25px 0;
        }
        .btn-back {
            width: 100%;
            padding: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
<div class="success-container">
    <div class="success-icon">
        <i class="bi bi-check-circle-fill"></i>
    </div>
    <h2>支付成功</h2>
    <p class="text-muted">感谢您的购买，订单已支付成功</p>

    <div class="order-info">
        <div class="row mb-2">
            <div class="col-6 text-muted">支付金额：</div>
            <div class="col-6" th:text="${'¥' + amount}">¥100.00</div>
        </div>
        <div class="row">
            <div class="col-6 text-muted">订单编号：</div>
            <div class="col-6" th:text="${out_trade_no}">20230725123456</div>
        </div>
    </div>

    <div class="d-grid gap-2">
        <a th:href="@{/}" class="btn btn-primary btn-back">返回首页</a>
    </div>

    <div class="mt-4 text-muted small">
        <p>支付完成，如有疑问请联系客服</p>
        <p>客服电话：400-123-4567</p>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>