<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>淘宝</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .payment-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }
        .payment-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .payment-header img {
            height: 40px;
            margin-bottom: 15px;
        }
        .form-label {
            font-weight: 500;
        }
        .btn-pay {
            background-color: #1677ff;
            border: none;
            width: 100%;
            padding: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .btn-pay:hover {
            background-color: #1261d1;
        }
    </style>
</head>
<body>
<div class="payment-container">
    <div class="payment-header">
        <h3>请支付订单</h3>
        <p class="text-muted">请填写支付信息</p>
    </div>

    <form th:action="@{/aliPay/pay}" method="post">
        <div class="mb-3">
            <label for="totalAmount" class="form-label">支付金额（元）</label>
            <div class="input-group">
                <span class="input-group-text">¥</span>
                <input type="number"
                       class="form-control"
                       id="totalAmount"
                       name="totalAmount"
                       placeholder="请输入支付金额"
                       min="0.01"
                       step="0.01"
                       required>
            </div>
            <div class="form-text">金额必须大于0，最多保留两位小数</div>
        </div>

        <div class="mb-4">
            <label for="subject" class="form-label">订单名称</label>
            <input type="text"
                   class="form-control"
                   id="subject"
                   name="subject"
                   placeholder="请输入订单名称"
                   required>
            <div class="form-text">请简要描述您的订单内容</div>
        </div>

        <button type="submit" class="btn btn-primary btn-pay">立即支付</button>
    </form>

    <div class="mt-4 text-center text-muted">
        <small>支付过程中遇到问题？请联系客服</small>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // 客户端验证
    document.querySelector('form').addEventListener('submit', function(e) {
        const amountInput = document.getElementById('totalAmount');
        const amount = parseFloat(amountInput.value);

        if (isNaN(amount) || amount <= 0) {
            alert('请输入有效的支付金额');
            amountInput.focus();
            e.preventDefault();
            return false;
        }

        const subjectInput = document.getElementById('subject');
        if (subjectInput.value.trim() === '') {
            alert('请输入订单名称');
            subjectInput.focus();
            e.preventDefault();
            return false;
        }

        return true;
    });
</script>
</body>
</html>